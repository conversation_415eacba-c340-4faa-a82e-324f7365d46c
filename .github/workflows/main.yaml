# Particle Compile Action Workflow
# This workflow uses the Particle compile-action to compile Particle application firmware.
# Make sure to set the particle-platform-name for your project.
# For complete documentation, please refer to https://github.com/particle-iot/compile-action

name: Particle Compile

on:
  push:
    branches:
      - main

jobs:
  compile:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      # Particle Compile Action
      - name: Compile Firmware
        id: compile
        uses: particle-iot/compile-action@v1
        with:
          # Set the particle-platform-name to the platform you're targeting.
          # Allowed values: core, photon, p1, electron, argon, boron, xenon, esomx, bsom, b5som, tracker, trackerm, p2, msom
          particle-platform-name: 'p2'

      # Optional: Upload compiled firmware as an artifact on GitHub.
      - name: Upload Firmware as Artifact
        uses: actions/upload-artifact@v3
        with:
          name: firmware-artifact
          path: |
            ${{ steps.compile.outputs.firmware-path }}
            ${{ steps.compile.outputs.target-path }}
