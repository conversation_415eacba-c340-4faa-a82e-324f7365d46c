/*
 * M404 MDD GATEWAY - FULL OPTIMIZED FIRMWARE
 * Version: 3.0.0
 * Device: Particle M404 with Muon Carrier Board
 * Description:
 *   - BLE Beacon scanning with UUID filter
 *   - Battery management with PMIC fault handling
 *   - Cloud publishing of scan and health data
 *   - Low power operation with sleep mode and recovery logic
 */

#include "Particle.h"
#include <vector>
#include <map>

SYSTEM_MODE(SEMI_AUTOMATIC);
SYSTEM_THREAD(ENABLED);

PRODUCT_VERSION(3);
#define FIRMWARE_VERSION "3.0.0"

// --- Config Constants ---
const int DEFAULT_SCAN_PERIOD = 8;
const int DEFAULT_POST_INTERVAL = 30;
const int MIN_RSSI_SAMPLES = 3;
const int HEALTH_INTERVAL = 300;

// --- Flags and Runtime State ---
bool debugEnabled = true;
bool scanInProgress = false;
bool cloudWasConnected = false;

// --- EEPROM Addresses ---
const int EEPROM_CONFIG_ADDR = 0;
const int EEPROM_HEALTH_ADDR = sizeof(int) * 4 + 512;  // config + padding

// --- BLE UUID Filter ---
std::vector<String> targetUUIDs;

// --- Timing ---
unsigned long lastPublishTime = 0;
unsigned long lastHealthCheckTime = 0;

// --- Data Structures ---
struct Config {
    int scanPeriod = DEFAULT_SCAN_PERIOD;
    int postInterval = DEFAULT_POST_INTERVAL;
    char uuidList[512] = "";
    bool valid = true;
} config;

struct BatteryState {
    float voltage = 0;
    float soc = 0;
    bool isCharging = false;
    bool wasCharging = false;
    float chargeStartSoc = 0;
    time_t chargeStartTime = 0;
    bool pmicFault = false;
} batteryState;

struct DeviceHealth {
    int bootCount = 0;
    float uptime = 0;
    int freeMemory = 0;
    int signalStrength = 0;
    int signalQuality = 0;
    float temperature = 0;
} deviceHealth;

struct BeaconData {
    String uuid;
    uint16_t major;
    uint16_t minor;
    std::vector<int8_t> rssiValues;
    time_t lastSeen;

    int getAvgRSSI() {
        if (rssiValues.empty()) return 0;
        int sum = 0;
        for (auto r : rssiValues) sum += r;
        return sum / rssiValues.size();
    }
};

std::map<String, BeaconData> beaconMap;

// --- Helper Functions ---
void parseUUIDList(const String& list) {
    targetUUIDs.clear();
    int start = 0, end = list.indexOf(',');
    while (end != -1) {
        String uuid = list.substring(start, end);
        uuid.trim();
        if (uuid.length()) targetUUIDs.push_back(uuid.toLowerCase());
        start = end + 1;
        end = list.indexOf(',', start);
    }
    String last = list.substring(start);
    last.trim();
    if (last.length()) targetUUIDs.push_back(last.toLowerCase());
}

void loadConfig() {
    EEPROM.get(EEPROM_CONFIG_ADDR, config);
    if (!config.valid || config.scanPeriod <= 0 || config.postInterval <= 0) {
        config = Config();
        EEPROM.put(EEPROM_CONFIG_ADDR, config);
    }
    parseUUIDList(String(config.uuidList));
}

void updateBattery() {
    FuelGauge fuel;
    if (fuel.begin()) {
        batteryState.voltage = fuel.getVCell();
        batteryState.soc = fuel.getSoC();
    }
    if (batteryState.voltage < 2.0 || batteryState.voltage > 5.0) batteryState.voltage = 3.7;
    if (batteryState.soc < 0 || batteryState.soc > 100) batteryState.soc = System.batteryCharge();

    PMIC pmic(true);
    if (pmic.begin()) {
        byte status = pmic.getSystemStatus();
        byte chrgStat = (status >> 4) & 0x03;
        batteryState.isCharging = (chrgStat == 0x01 || chrgStat == 0x02);
        batteryState.pmicFault = (pmic.getFault() > 0);
    }
}

void checkChargingState() {
    if (batteryState.isCharging && !batteryState.wasCharging) {
        batteryState.chargeStartTime = Time.now();
        batteryState.chargeStartSoc = batteryState.soc;
        Particle.publish("battery-event", "{\"event\":\"charge_start\"}", PRIVATE);
    } else if (!batteryState.isCharging && batteryState.wasCharging) {
        float duration = (Time.now() - batteryState.chargeStartTime) / 3600.0;
        float gain = batteryState.soc - batteryState.chargeStartSoc;
        Particle.publish("battery-event", String::format("{\"event\":\"charge_stop\",\"gain\":%.1f,\"hrs\":%.2f}", gain, duration), PRIVATE);
    }
    batteryState.wasCharging = batteryState.isCharging;
}

void updateHealth() {
    deviceHealth.uptime = System.uptime() / 3600.0;
    deviceHealth.freeMemory = System.freeMemory();
    if (Cellular.ready()) {
        CellularSignal sig = Cellular.RSSI();
        deviceHealth.signalStrength = sig.getStrengthValue();
        deviceHealth.signalQuality = sig.getQuality();
    }
    // Optional: temp sensor logic here
}

void publishHealth() {
    String data = String::format(
        "{\"uptime\":%.2f,\"soc\":%.1f,\"voltage\":%.2f,\"charging\":%s,\"mem\":%d,\"sig\":%d}",
        deviceHealth.uptime, batteryState.soc, batteryState.voltage,
        batteryState.isCharging ? "true" : "false",
        deviceHealth.freeMemory, deviceHealth.signalStrength);
    Particle.publish("device-health", data, PRIVATE);
}

void startBLEScan();
void stopBLEScan();

void processScanResult(const BleScanResult *scanResult, void *context) {
    uint8_t buf[BLE_MAX_ADV_DATA_LEN];
    size_t len = scanResult->advertisingData().get(BleAdvertisingDataType::MANUFACTURER_SPECIFIC_DATA, buf, BLE_MAX_ADV_DATA_LEN);
    if (len >= 25 && buf[0] == 0x4C && buf[1] == 0x00 && buf[2] == 0x02 && buf[3] == 0x15) {
        char uuid[37];
        snprintf(uuid, sizeof(uuid), "%02x%02x%02x%02x-%02x%02x-%02x%02x-%02x%02x-%02x%02x%02x%02x%02x%02x",
                 buf[4], buf[5], buf[6], buf[7], buf[8], buf[9], buf[10], buf[11],
                 buf[12], buf[13], buf[14], buf[15], buf[16], buf[17], buf[18], buf[19]);
        String uuidStr = String(uuid).toLowerCase();
        if (!targetUUIDs.empty()) {
            bool matched = false;
            for (auto &u : targetUUIDs) if (uuidStr == u) matched = true;
            if (!matched) return;
        }
        uint16_t major = (buf[20] << 8) | buf[21];
        uint16_t minor = (buf[22] << 8) | buf[23];
        String key = uuidStr + String(major) + String(minor);
        if (beaconMap.find(key) == beaconMap.end()) {
            beaconMap[key] = BeaconData{uuidStr, major, minor};
        }
        beaconMap[key].rssiValues.push_back(scanResult->rssi());
        beaconMap[key].lastSeen = Time.now();
    }
}

void startBLEScan() {
    if (!scanInProgress) {
        BleScanParams p = {sizeof(BleScanParams), 160, 80, (uint16_t)(config.scanPeriod * 100), false, BLE_SCAN_FP_ACCEPT_ALL};
        BLE.setScanParameters(&p);
        BLE.scan(processScanResult, nullptr);
        scanInProgress = true;
    }
}

void stopBLEScan() {
    if (scanInProgress) {
        BLE.stopScanning();
        scanInProgress = false;
    }
}

void publishBeaconData() {
    String data = "[";
    bool first = true;
    for (auto &pair : beaconMap) {
        auto &b = pair.second;
        if (b.rssiValues.size() >= MIN_RSSI_SAMPLES) {
            if (!first) data += ",";
            first = false;
            data += String::format("{\"uuid\":\"%s\",\"major\":%d,\"minor\":%d,\"rssi\":%d}",
                b.uuid.c_str(), b.major, b.minor, b.getAvgRSSI());
        }
    }
    data += "]";
    Particle.publish("beaconScanResults", data, PRIVATE);
    beaconMap.clear();
}

// --- Setup ---
void setup() {
    Serial.begin(115200);
    waitFor(Serial.isConnected, 5000);
    Log.info("Starting Gateway Firmware v%s", FIRMWARE_VERSION);

    BLE.on();
    loadConfig();
    updateBattery();

    SystemPowerConfiguration pwr;
    pwr.powerSourceMinVoltage(4200).powerSourceMaxCurrent(3000).batteryChargeCurrent(1024).batteryChargeVoltage(4208);
    System.setPowerConfiguration(pwr);

    Particle.connect();
    lastPublishTime = millis();
    lastHealthCheckTime = millis();
}

// --- Loop ---
void loop() {
    updateBattery();
    checkChargingState();

    unsigned long now = millis();
    unsigned long cycleTime = (now - lastPublishTime) % (config.postInterval * 1000);
    if (cycleTime < (config.scanPeriod * 1000)) {
        startBLEScan();
    } else {
        stopBLEScan();
    }

    if (now - lastPublishTime >= (unsigned long)(config.postInterval * 1000)) {
        publishBeaconData();
        lastPublishTime = now;
    }

    if (now - lastHealthCheckTime >= HEALTH_INTERVAL * 1000) {
        updateHealth();
        publishHealth();
        lastHealthCheckTime = now;
    }

    Particle.process();
}